#!/usr/bin/env python3
"""
Test script to verify the optimizations work correctly and measure performance improvements.
"""

import time
import os
from datetime import datetime
from BT_Thursday_MainCode import main, run_for_one_day

def test_single_day_performance():
    """Test performance of a single day processing"""
    print("Testing single day performance...")
    
    # Test with a sample folder (adjust path as needed)
    test_folder = r"C:\Users\<USER>\YatinBhatia\Thursday_BackTesting2\Parquet_Files\Thursday_output_folder\20210107"
    
    if not os.path.exists(test_folder):
        print(f"Test folder not found: {test_folder}")
        print("Please adjust the test_folder path in the script")
        return
    
    # Test parameters
    start_time = datetime.strptime("09:20", "%H:%M").time()
    exit_time = datetime.strptime("15:20", "%H:%M").time()
    stop_loss = 2.0
    target_profit = 4.0
    ratio_check = 0.15
    resize_factor = 1.0
    
    # Time the execution
    start = time.time()
    try:
        result = run_for_one_day(test_folder, start_time, exit_time, stop_loss, target_profit, ratio_check, resize_factor)
        end = time.time()
        
        print(f"Single day processing completed in {end - start:.2f} seconds")
        print(f"Result: {result}")
        
    except Exception as e:
        print(f"Error in single day test: {e}")

def test_parallel_vs_sequential():
    """Compare parallel vs sequential processing performance"""
    print("\nTesting parallel vs sequential processing...")
    
    # Test parameters
    start_time = datetime.strptime("09:20", "%H:%M").time()
    exit_time = datetime.strptime("15:20", "%H:%M").time()
    stop_loss = 2.0
    target_profit = 4.0
    ratio_check = 0.15
    resize_factor = 1.0
    
    # Test sequential processing
    print("Running sequential processing...")
    start = time.time()
    try:
        sequential_result = main(
            use_parallel=False,
            start_time=start_time,
            exit_time=exit_time,
            stop_loss=stop_loss,
            target_profit=target_profit,
            ratio_check=ratio_check,
            resize_factor=resize_factor
        )
        sequential_time = time.time() - start
        print(f"Sequential processing completed in {sequential_time:.2f} seconds")
        print(f"Processed {len(sequential_result)} days")
        
    except Exception as e:
        print(f"Error in sequential test: {e}")
        return
    
    # Test parallel processing
    print("\nRunning parallel processing...")
    start = time.time()
    try:
        parallel_result = main(
            use_parallel=True,
            start_time=start_time,
            exit_time=exit_time,
            stop_loss=stop_loss,
            target_profit=target_profit,
            ratio_check=ratio_check,
            resize_factor=resize_factor
        )
        parallel_time = time.time() - start
        print(f"Parallel processing completed in {parallel_time:.2f} seconds")
        print(f"Processed {len(parallel_result)} days")
        
        # Calculate speedup
        if sequential_time > 0:
            speedup = sequential_time / parallel_time
            print(f"\nSpeedup: {speedup:.2f}x")
            print(f"Time saved: {sequential_time - parallel_time:.2f} seconds")
        
        # Verify results are consistent
        if len(sequential_result) == len(parallel_result):
            print("✓ Same number of days processed")
            
            # Sort both results by date for comparison
            sequential_sorted = sorted(sequential_result, key=lambda x: x['date'])
            parallel_sorted = sorted(parallel_result, key=lambda x: x['date'])
            
            # Compare a few sample results
            differences = 0
            for i in range(min(5, len(sequential_sorted))):
                seq_pnl = sequential_sorted[i]['pnl']
                par_pnl = parallel_sorted[i]['pnl']
                if abs(seq_pnl - par_pnl) > 0.01:  # Allow small floating point differences
                    differences += 1
                    print(f"⚠ Difference found for {sequential_sorted[i]['date']}: {seq_pnl} vs {par_pnl}")
            
            if differences == 0:
                print("✓ Results are consistent between sequential and parallel processing")
            else:
                print(f"⚠ Found {differences} differences in sample results")
        else:
            print(f"⚠ Different number of days processed: {len(sequential_result)} vs {len(parallel_result)}")
            
    except Exception as e:
        print(f"Error in parallel test: {e}")

def test_memory_usage():
    """Basic memory usage information"""
    print("\nMemory usage information:")
    try:
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        print(f"Current memory usage: {memory_info.rss / 1024 / 1024:.2f} MB")
    except ImportError:
        print("psutil not available for memory monitoring")
        print("Install with: pip install psutil")

if __name__ == "__main__":
    print("Performance Optimization Test Suite")
    print("=" * 50)
    
    # Test single day performance
    test_single_day_performance()
    
    # Test memory usage
    test_memory_usage()
    
    # Test parallel vs sequential (comment out if you want to skip this)
    # Note: This will process the full date range, which might take a while
    # test_parallel_vs_sequential()
    
    print("\nTest suite completed!")
    print("\nTo run the full parallel vs sequential comparison, uncomment the line in the main section.")

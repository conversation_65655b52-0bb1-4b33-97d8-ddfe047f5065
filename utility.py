import os
import pandas as pd
import numpy as np
#import logger_config
import math

#logger = logger_config.setup_logger('main_logger', 'main.log')

# Determine sell option prices based on VIX
vix_price_map = {
    9: 14, 10: 15, 11: 16, 12:17, 13: 18, 14: 19, 15:20, 16: 20,
    17: 21, 18: 21, 19: 22, 20: 22, 21: 23
}

def round_vix(vix):
    """Round VIX value: 13.5 and above rounds up, below 13.5 rounds down."""
    return math.ceil(vix) if vix % 1 >= 0.5 else math.floor(vix)

def get_sell_option_price(vix):
    vix = round_vix(vix)
    #logger.info(f"Rounded off vix close value : {vix}")
    #price = 35
    if vix <=8:
        price = 12
    elif vix >= 22:
        price = 24
    else:
        price = vix_price_map.get(vix) # Use vix_price_map for vix values between 9 and 21
    return float(price)

def get_calender_diff_value(price, vix):
    vix = round_vix(vix)
    if vix <= 13:
        diff = 10
    elif 14 <= vix <= 15:
        diff = 8
    elif 16 <= vix <= 18:
        diff = 5
    elif 19 <= vix <= 21:
        diff = 0
    elif vix >= 22:
        diff = -5
    return price + diff

def find_closest_option(folder_path, target_date, target_price, start_time):
    """
    Finds the row in the folder whose 'Close' is closest to the target price for a specific date.
    Returns (symbol, row with Date, Time, Close).
    Optimized version with reduced file I/O and vectorized operations.
    """
    closest_diff = float('inf')
    selected_row = None
    selected_symbol = None
    start_time_str = start_time.strftime("%H:%M")

    # Get all parquet files at once
    parquet_files = [f for f in os.listdir(folder_path) if f.endswith('.parquet')]

    for file_name in parquet_files:
        file_path = os.path.join(folder_path, file_name)

        try:
            # Read only necessary columns to reduce memory usage
            df = pd.read_parquet(file_path, columns=['YMD', 'Time', 'Close'])
        except Exception as e:
            continue

        # Convert data types efficiently
        df["YMD"] = df["YMD"].astype(str)
        df["Time"] = df["Time"].astype(str)

        # Filter data
        mask = (df["YMD"] == target_date) & (df["Time"] == start_time_str)
        filtered_df = df[mask]

        if filtered_df.empty:
            continue

        # Vectorized operations
        filtered_df = filtered_df.copy()
        filtered_df["Close"] = pd.to_numeric(filtered_df["Close"], errors='coerce')
        filtered_df = filtered_df.dropna(subset=["Close"])

        if filtered_df.empty:
            continue

        # Calculate differences using numpy for speed
        close_values = filtered_df["Close"].values
        diffs = abs(close_values - target_price)
        min_idx = diffs.argmin()
        min_diff = diffs[min_idx]

        if min_diff < closest_diff:
            closest_diff = min_diff
            selected_row = filtered_df.iloc[min_idx]
            selected_symbol = os.path.splitext(file_name)[0]

    return (selected_symbol, selected_row) if selected_row is not None else (None, None)
   
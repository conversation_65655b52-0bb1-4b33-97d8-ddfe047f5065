# Performance Optimization Summary

## Overview
This document summarizes the optimizations made to reduce execution time without changing the core logic of the backtesting system.

## Key Optimizations Implemented

### 1. Data Loading and Processing Optimizations

#### Monitor_Prices.py
- **Replaced Dask with Pandas**: For small datasets, pandas is more efficient than dask
- **Column Selection**: Read only necessary columns (`YMD`, `Time`, `Close`) to reduce memory usage
- **Vectorized Operations**: 
  - Used numpy for min/max calculations instead of pandas operations
  - Pre-calculated PnL for all rows using vectorized operations
  - Eliminated row-by-row PnL calculations in the main loop
- **Efficient Merging**: Used pandas merge instead of dask merge for better performance
- **Memory Optimization**: Used `.copy()` to avoid SettingWithCopyWarning and ensure clean data

#### VIX_Threshold.py
- **Indexed DataFrame**: Set datetime as index for O(1) lookup instead of O(n) filtering
- **LRU Cache**: Added `@lru_cache(maxsize=1000)` to cache frequently accessed VIX values
- **Direct Index Access**: Used `df.loc[datetime]` instead of filtering operations

#### utility.py (find_closest_option)
- **Reduced File I/O**: Get all parquet files at once instead of checking each file individually
- **Column Selection**: Read only necessary columns to reduce memory usage
- **Vectorized Calculations**: Used numpy for difference calculations instead of pandas operations
- **Efficient Data Types**: Optimized data type conversions

### 2. Parallel Processing

#### BT_Thursday_MainCode.py
- **Multi-Processing**: Added parallel processing capability using `ProcessPoolExecutor`
- **Configurable Workers**: Limited to 4 workers or CPU count, whichever is smaller
- **Wrapper Function**: Created wrapper for parallel execution with proper error handling
- **Backward Compatibility**: Maintained option for sequential processing

### 3. Caching and Memory Optimizations

#### total_pnl.py
- **Environment Variable Caching**: Cached `GLOBAL_QTY` using `@lru_cache` to avoid repeated lookups
- **Price Mapping**: Pre-defined price mapping dictionary for faster lookups
- **Optimized Logic**: Reduced conditional checks and function calls
- **Early Termination**: Skip closed positions early in the loop

### 4. Algorithm Improvements

#### Monitor_Prices.py - Vectorized PnL Calculation
- **New Function**: `calculate_vectorized_pnl()` calculates PnL for all time points at once
- **Numpy Arrays**: Used numpy arrays for faster iteration over results
- **Batch Processing**: Process all time points in vectorized operations before checking exit conditions

## Performance Benefits Expected

### 1. I/O Optimizations
- **Reduced File Reads**: Reading only necessary columns reduces I/O time by ~30-50%
- **Indexed VIX Lookups**: O(1) instead of O(n) lookups for VIX data
- **Cached Values**: Eliminates repeated environment variable and VIX lookups

### 2. Computational Optimizations
- **Vectorized Operations**: 5-10x faster than row-by-row operations for mathematical calculations
- **Reduced Function Calls**: Eliminates overhead from repeated function calls in loops
- **Memory Efficiency**: Lower memory usage reduces garbage collection overhead

### 3. Parallel Processing
- **Multi-Core Utilization**: Can process multiple days simultaneously
- **Scalability**: Performance scales with available CPU cores
- **Estimated Speedup**: 2-4x faster for large date ranges (depending on CPU cores)

## Usage Instructions

### Sequential Processing (Original)
```python
from BT_Thursday_MainCode import main
equity_log = main(use_parallel=False)
```

### Parallel Processing (Optimized)
```python
from BT_Thursday_MainCode import main
equity_log = main(use_parallel=True)
```

### Custom Parameters
```python
from datetime import datetime
equity_log = main(
    use_parallel=True,
    start_time=datetime.strptime("09:20", "%H:%M").time(),
    exit_time=datetime.strptime("15:20", "%H:%M").time(),
    stop_loss=2.0,
    target_profit=4.0,
    ratio_check=0.15,
    resize_factor=1.0
)
```

## Compatibility Notes

- All optimizations maintain backward compatibility
- Core logic and calculations remain unchanged
- Results should be identical to the original implementation
- Added error handling for robustness in parallel processing

## Monitoring Performance

To measure the performance improvement:

1. **Time the execution** before and after optimizations
2. **Monitor memory usage** during processing
3. **Check CPU utilization** when using parallel processing
4. **Verify results consistency** between optimized and original versions

## Future Optimization Opportunities

1. **Database Integration**: Store processed data in a database for faster subsequent access
2. **Incremental Processing**: Only process new dates instead of reprocessing all data
3. **Memory Mapping**: Use memory-mapped files for very large datasets
4. **GPU Acceleration**: Use libraries like CuPy for GPU-accelerated computations
5. **Distributed Processing**: Use Dask or Ray for processing across multiple machines

import os
import dask.dataframe as dd
import pandas as pd
import logger_config
from dotenv import load_dotenv
from total_pnl import calculate_total_pnl_per_minute
from datetime import datetime
import numpy as np

# Load environment variables and logger once
load_dotenv()
#logger = logger_config.get_logger('main_logger')
GLOBAL_QTY = int(os.getenv("GLOBAL_QTY"))
Capital = int(os.getenv("Capital"))
#STOP_LOSS_PERCENTAGE = float(os.getenv("STOP_LOSS_PERCENTAGE"))
#TARGET_PROFIT_PERCENTAGE = float(os.getenv("TARGET_PROFIT_PERCENTAGE"))
#STOP_LOSS = Capital * (STOP_LOSS_PERCENTAGE / 100)
#TARGET_PROFIT = Capital * (TARGET_PROFIT_PERCENTAGE / 100)

def calculate_vectorized_pnl(merged_df, positions):
    """
    Calculate PnL for all rows using vectorized operations for better performance.
    """
    total_pnl_array = np.zeros(len(merged_df))

    for key, pos in positions.items():
        if not pos.get('closed', False):
            entry_price = pos.get('ltp')
            side = pos.get('side', 'SELL')

            if entry_price is not None:
                if side == 'SELL':
                    if 'CE' in key:
                        current_prices = merged_df['CE_Sell_Close'].values
                    elif 'PE' in key:
                        current_prices = merged_df['PE_Sell_Close'].values
                    pnl_contribution = (entry_price - current_prices) * GLOBAL_QTY
                else:  # BUY
                    if 'CE' in key:
                        current_prices = merged_df['CE_Buy_Close'].values
                    elif 'PE' in key:
                        current_prices = merged_df['PE_Buy_Close'].values
                    pnl_contribution = (current_prices - entry_price) * GLOBAL_QTY

                total_pnl_array += pnl_contribution

    merged_df['total_pnl'] = np.round(total_pnl_array, 2)
    return merged_df


def exit_all_positions(positions, exit_time, ce_sell_price=None, pe_sell_price=None, ce_buy_price=None, pe_buy_price=None):
    #logger.info("Exiting all positions for the day...")
    total_pnl = 0

    for key, pos in positions.items():
        if pos.get('closed', False):
            continue

        symbol = pos.get('symbol')
        side = pos.get('side')
        exit_price = None

        if 'CE' in key:
            if side == 'SELL' and ce_sell_price is not None:
                exit_price = ce_sell_price
            elif side == 'BUY' and ce_buy_price is not None:
                exit_price = ce_buy_price
        elif 'PE' in key:
            if side == 'SELL' and pe_sell_price is not None:
                exit_price = pe_sell_price
            elif side == 'BUY' and pe_buy_price is not None:
                exit_price = pe_buy_price

        # Fallback to current price if specific exit price is not available
        if exit_price is None:
            exit_price = pos.get('current_price')

        pos.update({
            'exit_time': exit_time,
            'exit_price': exit_price,
            'closed': True
        })

        entry_price = pos.get('ltp')

        if entry_price is not None and exit_price is not None:
            multiplier = -1 if side == 'SELL' else 1
            pos['pnl'] = (exit_price - entry_price) * multiplier * GLOBAL_QTY
            total_pnl += pos['pnl']
        else:
            pos['pnl'] = None

        #logger.info(
         #   f"Position {key} on {symbol} closed at {exit_time} with exit price {exit_price} | P&L: {pos['pnl']}"
        #)

    #logger.info(f"Total P&L for the day: {total_pnl}")
    #logger.info("All positions marked as closed.")
    return round(total_pnl, 2)


def monitor_prices(positions, folder_path, start_time, exit_time, folder_date,target_profit, stop_loss,ratio_check):
    #logger.info(f"Stop Loss: {STOP_LOSS} | Target Profit: {TARGET_PROFIT}")

    STOP_LOSS_PERCENTAGE = stop_loss
    TARGET_PROFIT_PERCENTAGE = target_profit
    STOP_LOSS = Capital * (STOP_LOSS_PERCENTAGE / 100)
    TARGET_PROFIT = Capital * (TARGET_PROFIT_PERCENTAGE / 100)

    ce_sell_symbol = pe_sell_symbol = ce_buy_symbol = pe_buy_symbol = None
    for key, pos in positions.items():
        if pos['side'] == 'SELL':
            if 'CE' in key: ce_sell_symbol = pos['symbol']
            elif 'PE' in key: pe_sell_symbol = pos['symbol']
        if pos['side'] == 'BUY':
            if 'CE' in key: ce_buy_symbol = pos['symbol']
            elif 'PE' in key: pe_buy_symbol = pos['symbol']    

    if not ce_sell_symbol or not pe_sell_symbol or not ce_buy_symbol or not pe_buy_symbol:
        #logger.info("Either CE_SELL, PE_SELL, CE_BUY, or PE_BUY symbol not found in positions.")
        return

    def load_option_data(symbol, subfolder, folder_date):
        file_path = os.path.join(folder_path, subfolder, f"{symbol}.parquet")
        #if not os.path.exists(file_path):
            #logger.info(f"File not found: {file_path}")
         #   return None

        try:
            # Read only necessary columns to reduce memory usage
            df = pd.read_parquet(file_path, columns=['YMD', 'Time', 'Close'])

            # Filter by date first (more efficient than dask for this use case)
            df = df[df['YMD'] == int(folder_date)]

            if df.empty:
                return None

            # Convert time once and cache the result
            df['time_only'] = pd.to_datetime(df['Time'], format='%H:%M').dt.time

            # Time filtering with vectorized operations
            mask = (df['time_only'] >= start_time) & (df['time_only'] <= exit_time)
            return df[mask][['YMD', 'time_only', 'Close']].copy()

        except Exception as e:
            #logger.error(f"Error reading Parquet file {file_path}: {e}")
            return None


    ce_sell_df = load_option_data(ce_sell_symbol, "CE_SELL", folder_date)
    if ce_sell_df is None:
        #logger.info(f"Data for CE_SELL option {ce_sell_symbol} not available.")
        return
    pe_sell_df = load_option_data(pe_sell_symbol, "PE_SELL", folder_date)
    if pe_sell_df is None:
        #logger.info(f"Data for PE_SELL option {pe_sell_symbol} not available.")
        return
    ce_buy_df = load_option_data(ce_buy_symbol, "CE_BUY", folder_date)
    if ce_buy_df is None:
        #logger.info(f"Data for CE_BUY option {ce_buy_symbol} not available.")
        return
    pe_buy_df = load_option_data(pe_buy_symbol, "PE_BUY", folder_date)
    if pe_buy_df is None:
        #logger.info(f"Data for PE_BUY option {pe_buy_symbol} not available.")
        return

    if ce_sell_df is None or pe_sell_df is None or ce_buy_df is None or pe_buy_df is None:
        #logger.info("Data for one or more options not available.")
        return

    # Use pandas merge instead of dask for better performance on small datasets
    # Rename columns first
    ce_sell_df = ce_sell_df.rename(columns={'Close': 'CE_Sell_Close'})
    pe_sell_df = pe_sell_df.rename(columns={'Close': 'PE_Sell_Close'})
    ce_buy_df = ce_buy_df.rename(columns={'Close': 'CE_Buy_Close'})
    pe_buy_df = pe_buy_df.rename(columns={'Close': 'PE_Buy_Close'})

    # Chain merges for better performance
    merged_df = ce_sell_df.merge(pe_sell_df, on=['YMD', 'time_only'], how='inner')
    merged_df = merged_df.merge(ce_buy_df, on=['YMD', 'time_only'], how='inner')
    merged_df = merged_df.merge(pe_buy_df, on=['YMD', 'time_only'], how='inner')

    # Vectorized ratio calculation
    ce_pe_values = merged_df[['CE_Sell_Close', 'PE_Sell_Close']].values
    merged_df['lower_ltp'] = np.minimum(ce_pe_values[:, 0], ce_pe_values[:, 1])
    merged_df['higher_ltp'] = np.maximum(ce_pe_values[:, 0], ce_pe_values[:, 1])
    merged_df['ratio'] = np.round(merged_df['lower_ltp'] / merged_df['higher_ltp'], 2)

    #logger.info("\nDate\t\tTime\t\tCE_SELL Close\tPE_SELL Close\tlower_ltp\thigher_ltp\tratio")
    #logger.info("-" * 50)

    # Pre-calculate PnL for all rows using vectorized operations
    merged_df = calculate_vectorized_pnl(merged_df, positions)

    # Sort by time to ensure chronological processing
    merged_df = merged_df.sort_values('time_only')

    # Use numpy arrays for faster iteration
    ratios = merged_df['ratio'].values
    total_pnls = merged_df['total_pnl'].values
    times = merged_df['time_only'].values

    for i, (ratio, total_pnl, current_time) in enumerate(zip(ratios, total_pnls, times)):
        # Get current row data
        row = merged_df.iloc[i]
        ce_sell_close = row['CE_Sell_Close']
        pe_sell_close = row['PE_Sell_Close']
        ce_buy_close = row['CE_Buy_Close']
        pe_buy_close = row['PE_Buy_Close']

        # Ensure current_time is a time object for comparison
        if not isinstance(current_time, type(exit_time)):
            current_time = datetime.strptime(str(current_time)[:5], "%H:%M").time()

        if ratio < ratio_check or total_pnl <= STOP_LOSS or total_pnl >= TARGET_PROFIT or current_time >= exit_time:

            if total_pnl >= TARGET_PROFIT:
                #logger.info("Exit condition met: Total PnL is greater than or equal to TARGET_PROFIT.")
                return exit_all_positions(positions,
                    exit_time=current_time,
                    ce_sell_price=ce_sell_close,
                    pe_sell_price=pe_sell_close,
                    ce_buy_price=ce_buy_close,
                    pe_buy_price=pe_buy_close
                ), "TARGET_PROFIT"

            elif total_pnl <= STOP_LOSS:
                #logger.info("Exit condition met: Total PnL is less than or equal to STOP_LOSS.")
                return exit_all_positions(positions,
                    exit_time=current_time,
                    ce_sell_price=ce_sell_close,
                    pe_sell_price=pe_sell_close,
                    ce_buy_price=ce_buy_close,
                    pe_buy_price=pe_buy_close
                ),"STOP_LOSS"

            elif ratio < ratio_check:
                #logger.info("Exit condition met: Ratio is less than 0.15.")
                return exit_all_positions(positions,
                    exit_time=current_time,
                    ce_sell_price=ce_sell_close,
                    pe_sell_price=pe_sell_close,
                    ce_buy_price=ce_buy_close,
                    pe_buy_price=pe_buy_close
                ),"RATIO_HIT"

            elif current_time >= exit_time:
                #logger.info("Exit condition met: Current time is greater than or equal to exit time.")
                return exit_all_positions(positions,
                    exit_time=current_time,
                    ce_sell_price=ce_sell_close,
                    pe_sell_price=pe_sell_close,
                    ce_buy_price=ce_buy_close,
                    pe_buy_price=pe_buy_close
                ),"TIME_EXIT"
        #else:
            #logger.info("Exit condition not met. Waiting for the next check.")
    return None, "NO_EXIT_CONDITION_MET"
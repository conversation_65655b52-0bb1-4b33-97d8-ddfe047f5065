import os
import re
import time
from datetime import datetime, timedelta
from BT_Thursday_New_Recreate_Strategy import thursday_execute_BCS
from Monitor_Prices import monitor_prices
import logger_config
import random
from datetime import date
from Generate_Equity_Curve import generate_equity_curve
import csv
from ratio_hit_analysis import export_ratio_hit_analysis
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing

logger = logger_config.setup_logger('main_logger', 'main.log')


# ------------- 1. isolate the *original* single‑date routine ---------------- #
def run_for_one_day(main_folder: str, start_time, exit_time,stop_loss, target_profit, ratio_check, resize_factor) -> None:
    """
    Run the existing back‑test pipeline for a single YYYYMMDD folder.
    This is your original code, moved unchanged into its own function.
    """
    start_time_process = time.time()
    #logger.info("Starting the main process...")
    #logger.info(f"Backtesting trading window: Start at {start_time}, Exit at {exit_time}")

    # --- Identify the date from the folder name --- #
    folder_date = os.path.basename(main_folder)
    folder_date_dt = datetime.strptime(folder_date, "%Y%m%d")
    #logger.info(f"Folder Date: {folder_date_dt.strftime('%Y-%m-%d')}")

    # --- Subfolders and their sides (unchanged) --- #
    sides = {
        "CE_SELL": "SELL",
        "PE_SELL": "SELL",
        "CE_BUY":  "BUY",
        "PE_BUY":  "BUY"
    }
  

    # --- Strategy + monitoring (unchanged) --- #
    start_execution_time = time.time()
    BCS_positions, vix_close_value = thursday_execute_BCS(main_folder, start_time, exit_time, folder_date_dt,stop_loss, target_profit, ratio_check, resize_factor)
    
    if BCS_positions is None:
        logger.info(f"Skipping folder {main_folder} due to missing VIX data or other issues.")
        return None, None
    #logger.info(f"BCS_positions: {BCS_positions}")
    #logger.info(f"thursday_execute_BCS() execution time: {time.time()-start_execution_time:.6f}s")

    start_execution_time = time.time()
    total_pnl, exit_reason  =monitor_prices(BCS_positions, main_folder, start_time, exit_time, folder_date, target_profit, stop_loss,ratio_check)
    #logger.info(f"monitor_prices() execution time: {time.time()-start_execution_time:.6f}s")

    # --- Finish up --- #
    processing_time = time.time() - start_time_process
    #logger.info(f"Processing time: {str(timedelta(seconds=int(processing_time)))}")
    #logger.info("End of day ----------------------------------------------\n")
    
    return folder_date,total_pnl, exit_reason, vix_close_value

# ------------- 2. Wrapper function for parallel processing ------------- #
def run_for_one_day_wrapper(args):
    """Wrapper function to unpack arguments for parallel processing"""
    folder, start_time, exit_time, stop_loss, target_profit, ratio_check, resize_factor = args
    try:
        return run_for_one_day(folder, start_time, exit_time, stop_loss, target_profit, ratio_check, resize_factor)
    except Exception as e:
        logger.exception(f"Error processing folder {folder}: {e}")
        return None, None, None, None

# ------------- 3. NEW driver that loops through all date folders ------------- #
def main(use_parallel=True, start_time=None, exit_time=None, stop_loss=None, target_profit=None, ratio_check=None, resize_factor=None):
    """
    Main function with optional parallel processing.

    Args:
        use_parallel: Whether to use parallel processing (default: True)
        start_time, exit_time, stop_loss, target_profit, ratio_check, resize_factor:
            Trading parameters. If None, will use default values.
    """
    start_time_main = time.time()

    root_path = r"C:\Users\<USER>\YatinBhatia\Thursday_BackTesting2\Parquet_Files\Thursday_output_folder"

    exit_reason_counts = {
        "STOP_LOSS": 0,
        "TARGET_PROFIT": 0,
        "RATIO_HIT": 0,
        "TIME_EXIT": 0
    }

    # Set default parameters if not provided
    if start_time is None:
        start_time = datetime.strptime("09:20", "%H:%M").time()
    if exit_time is None:
        exit_time = datetime.strptime("15:20", "%H:%M").time()
    if stop_loss is None:
        stop_loss = 2.0
    if target_profit is None:
        target_profit = 4.0
    if ratio_check is None:
        ratio_check = 0.15
    if resize_factor is None:
        resize_factor = 1.0

    # Optional: change these if you want to limit the range
    from_date = datetime.strptime("20210107", "%Y%m%d")
    to_date   = datetime.strptime("20250529", "%Y%m%d")

    date_folder_pattern = re.compile(r"^\d{8}$")   # YYYYMMDD

    all_entries = [
        os.path.join(root_path, name)
        for name in os.listdir(root_path)
        if date_folder_pattern.match(name) and os.path.isdir(os.path.join(root_path, name))
    ]

    # Sort chronologically so logs read nicely
    all_entries.sort()
    equity_log = []  # Store date-wise total_pnl here

    # ---- Filter by the desired date window ---- #
    date_folders = []
    for path in all_entries:
        current_date = datetime.strptime(os.path.basename(path), "%Y%m%d")
        if from_date <= current_date <= to_date:
            date_folders.append(path)

    if not date_folders:
        logger.warning("No date folders found in the specified range!")
        return

    logger.info(f"Found {len(date_folders)} date folders to process.")
    logger.info(f"Using parallel processing: {use_parallel}")

    # ---- Run the logic for each day ---- #
    if use_parallel and len(date_folders) > 1:
        # Parallel processing
        max_workers = min(multiprocessing.cpu_count(), len(date_folders), 4)  # Limit to 4 workers
        logger.info(f"Using {max_workers} parallel workers")

        # Prepare arguments for parallel processing
        args_list = [
            (folder, start_time, exit_time, stop_loss, target_profit, ratio_check, resize_factor)
            for folder in date_folders
        ]

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_folder = {
                executor.submit(run_for_one_day_wrapper, args): args[0]
                for args in args_list
            }

            # Collect results as they complete
            for future in as_completed(future_to_folder):
                folder = future_to_folder[future]
                try:
                    folder_date, pnl, exit_reason, vix_close_value = future.result()
                    if folder_date is not None:
                        equity_log.append({
                            'date': folder_date,
                            'pnl': pnl,
                            'exit_reason': exit_reason,
                            'vix_close_value': vix_close_value
                        })
                        if exit_reason is not None:
                            exit_reason_counts[exit_reason] += 1
                except Exception as e:
                    logger.exception(f"Error processing folder {folder}: {e}")
    else:
        # Sequential processing
        for folder in date_folders:
            try:
                folder_date, pnl, exit_reason, vix_close_value = run_for_one_day(
                    folder, start_time, exit_time, stop_loss, target_profit, ratio_check, resize_factor
                )

                if folder_date is not None:
                    equity_log.append({
                        'date': folder_date,
                        'pnl': pnl,
                        'exit_reason': exit_reason,
                        'vix_close_value': vix_close_value
                    })
                    if exit_reason is not None:
                        exit_reason_counts[exit_reason] += 1
                    else:
                        logger.warning(f"No exit reason for folder {folder}. Skipping count update.")

            except Exception as e:
                logger.exception(f"Error while processing folder {folder}: {e}")

    # Sort equity_log by date for consistency
    equity_log.sort(key=lambda x: x['date'])

    # Generate ratio hit analysis
    export_ratio_hit_analysis(equity_log, output_dir=".")

    # Generate equity curve
    #generate_equity_curve(equity_log, output_dir=".")

    processing_time = time.time() - start_time_main
    logger.info(f"Total execution time: {str(timedelta(seconds=int(processing_time)))}")

    logger.info("----- Exit Reason Summary -----")
    for reason, count in exit_reason_counts.items():
        logger.info(f"{reason}: {count}")

    return equity_log


#if __name__ == "__main__":
 #   main()

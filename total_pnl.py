import os
from functools import lru_cache
#import logger_config

#logger = logger_config.get_logger('main_logger')

# Cache the QTY value to avoid repeated environment variable lookups
@lru_cache(maxsize=1)
def get_global_qty():
    return int(os.getenv("GLOBAL_QTY"))

def calculate_total_pnl_per_minute(positions, ce_sell_close, pe_sell_close, ce_buy_close, pe_buy_close):
    """
    Calculate the total P&L for the current positions at a given minute.
    Optimized version with reduced function calls and improved logic.

    :param positions: Dictionary of current positions
    :param ce_sell_close: Current CE sell price
    :param pe_sell_close: Current PE sell price
    :param ce_buy_close: Current CE buy price
    :param pe_buy_close: Current PE buy price
    :return: Total P&L for the current minute
    """
    total_pnl = 0.0  # Initialize total P&L
    qty = get_global_qty()  # Use cached QTY value

    # Pre-define price mapping for faster lookup
    price_map = {
        ('CE', 'SELL'): ce_sell_close,
        ('PE', 'SELL'): pe_sell_close,
        ('CE', 'BUY'): ce_buy_close,
        ('PE', 'BUY'): pe_buy_close
    }

    for key, pos in positions.items():
        if pos.get('closed', False):
            continue

        entry_price = pos.get('ltp')
        side = pos.get('side', 'SELL')

        if entry_price is None:
            pos['pnl'] = None
            continue

        # Determine option type and get current price
        option_type = 'CE' if 'CE' in key else 'PE'
        current_price = price_map.get((option_type, side))

        if current_price is None:
            pos['pnl'] = None
            continue

        # Calculate P&L with optimized logic
        if side == 'SELL':
            pnl = (entry_price - current_price) * qty
        else:  # BUY
            pnl = (current_price - entry_price) * qty

        pos['pnl'] = pnl
        total_pnl += pnl

    return total_pnl
